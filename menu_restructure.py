import json
import csv
from typing import Dict, List, Set, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict
import logging
from pathlib import Path
import argparse
import os
from datetime import datetime

# Load configuration from config.json
def load_config():
    config_path = os.path.join(os.path.dirname(__file__), 'config.json')
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        return config
    except Exception as e:
        logging.error(f"Error loading config file: {e}")
        return {}

def setup_logging(log_dir: str = 'logs', log_level: str = 'INFO') -> logging.Logger:
    """Setup logging with file and console handlers.
    
    Args:
        log_dir: Directory to store log files
        log_level: Logging level (e.g., 'INFO', 'DEBUG')
        
    Returns:
        Configured logger instance
    """
    # Ensure log directory exists
    os.makedirs(log_dir, exist_ok=True)
    
    # Create a timestamp for the log file
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_file = os.path.join(log_dir, f'menu_restructure_{timestamp}.log')
    
    # Create formatter
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    
    # Create file handler
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setFormatter(formatter)
    
    # Create console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    
    # Get logger and set level
    logger = logging.getLogger('menu_restructure')
    logger.setLevel(getattr(logging, log_level.upper(), 'INFO'))
    
    # Clear any existing handlers
    logger.handlers = []
    
    # Add handlers
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    logger.info(f"Logging to file: {os.path.abspath(log_file)}")
    return logger

# Global config
CONFIG = load_config()

# Setup logging
logger = setup_logging(
    log_dir=CONFIG.get('log_dir', 'logs'),
    log_level=CONFIG.get('log_level', 'INFO')
)

# --- Data Models ---

@dataclass
class Option:
    _id: str
    title: str
    price: float
    description: Optional[str] = None

@dataclass
class Addon:
    _id: str
    title: str
    options: List[str]  # List of option _ids
    quantity_minimum: int
    quantity_maximum: int
    description: Optional[str] = None

@dataclass
class Variation:
    _id: str
    title: str
    price: float
    discounted: Optional[float] = None
    addons: List[str] = None  # List of addon _ids

@dataclass
class Food:
    _id: str
    title: str
    description: str
    variations: List[Variation]
    image: Optional[str] = None
    is_active: bool = True

@dataclass
class Category:
    _id: str
    title: str
    foods: List[Food]

@dataclass
class Restaurant:
    _id: str
    name: str
    categories: List[Category]
    addons: List[Addon]
    options: List[Option]
    image: Optional[str] = None

# --- Helper Functions ---

def generate_id() -> str:
    """Generate a unique ID."""
    import uuid
    return str(uuid.uuid4())

def sanitize_for_csv(value: Any) -> str:
    """Convert value to string and escape CSV special characters."""
    if value is None:
        return ''
    return str(value).replace('"', '""')

# --- Main Transformation Logic ---

def extract_options(menu_data: dict) -> Tuple[Dict[str, Option], Dict[str, str], Dict[tuple, str]]:
    """Step 1: Extract all MenuItemOptionSetItems as Options.
    
    Returns:
        Tuple containing:
        - Dictionary of unique options (option_id -> Option)
        - Mapping from original option IDs to new option IDs
        - Mapping from option content to option ID for deduplication
    """
    logging.info("Step 1: Extracting options...")
    options = {}  # option_id -> Option
    option_refs = {}  # Original ID -> New Option ID
    content_to_id = {}  # (title, price, description) -> option_id
    
    for section in menu_data.get("MenuSections", []):
        for item in section.get("MenuItems", []):
            for option_set in item.get("MenuItemOptionSets", []):
                for option_item in option_set.get("MenuItemOptionSetItems", []):
                    # Create option content key for deduplication
                    title = (option_item.get("Name") or "").strip()
                    price = float(option_item.get("Price", 0.0))
                    description = option_item.get("Description")
                    content_key = (title, price, description)
                    
                    # Check if we've seen this content before
                    if content_key in content_to_id:
                        # Use existing option ID
                        option_id = content_to_id[content_key]
                    else:
                        # Create new option
                        option = Option(
                            _id=option_item.get("MenuItemOptionSetItemId") or generate_id(),
                            title=title,
                            price=price,
                            description=description
                        )
                        options[option._id] = option
                        content_to_id[content_key] = option._id
                        option_id = option._id
                    
                    # Map original ID to new ID
                    original_id = option_item.get("MenuItemOptionSetItemId")
                    if original_id and original_id not in option_refs:
                        option_refs[original_id] = option_id
    
    logging.info(f"Extracted {len(options)} unique options from {len(option_refs)} original options")
    return options, option_refs, content_to_id

def extract_addons(menu_data: dict, option_refs: Dict[str, str]) -> Tuple[Dict[str, Addon], Dict[str, str], Dict[tuple, str]]:
    """Step 2: Extract all MenuItemOptionSets as Addons.
    
    Returns:
        Tuple containing:
        - Dictionary of unique addons (addon_id -> Addon)
        - Mapping from original addon IDs to new addon IDs
        - Mapping from addon content to addon ID for deduplication
    """
    logging.info("Step 2: Extracting addons...")
    addons = {}  # addon_id -> Addon
    addon_refs = {}  # Original ID -> New Addon ID
    content_to_id = {}  # (title, tuple(sorted(options)), min, max, description) -> addon_id
    
    for section in menu_data.get("MenuSections", []):
        for item in section.get("MenuItems", []):
            for option_set in item.get("MenuItemOptionSets", []):
                # Skip if no options in this set
                if not option_set.get("MenuItemOptionSetItems"):
                    continue
                
                # Get addon properties
                title = (option_set.get("Name") or "").strip()
                min_select = option_set.get("MinSelectCount", 0)
                max_select = option_set.get("MaxSelectCount", 0)
                description = option_set.get("Description")
                
                # Get and sort option IDs for consistent comparison
                option_ids = []
                for option_item in option_set.get("MenuItemOptionSetItems", []):
                    original_id = option_item.get("MenuItemOptionSetItemId")
                    if original_id and original_id in option_refs:
                        option_id = option_refs[original_id]
                        if option_id not in option_ids:
                            option_ids.append(option_id)
                
                # Sort option IDs for consistent comparison
                option_ids_sorted = tuple(sorted(option_ids))
                
                # Create content key for deduplication
                content_key = (title, option_ids_sorted, min_select, max_select, description)
                
                # Check if we've seen this addon before
                if content_key in content_to_id:
                    # Use existing addon ID
                    addon_id = content_to_id[content_key]
                else:
                    # Create new addon
                    addon = Addon(
                        _id=option_set.get("MenuItemOptionSetId") or generate_id(),
                        title=title,
                        options=option_ids,
                        quantity_minimum=min_select,
                        quantity_maximum=max_select,
                        description=description
                    )
                    addons[addon._id] = addon
                    content_to_id[content_key] = addon._id
                    addon_id = addon._id
                
                # Map original ID to new ID
                original_addon_id = option_set.get("MenuItemOptionSetId")
                if original_addon_id and original_addon_id not in addon_refs:
                    addon_refs[original_addon_id] = addon_id
    
    logging.info(f"Extracted {len(addons)} unique addons from {len(addon_refs)} original addons")
    return addons, addon_refs, content_to_id

def extract_foods(menu_data: dict, addon_refs: Dict[str, str]) -> Tuple[Dict[str, Food], Dict[str, str]]:
    """Step 3: Extract all MenuItems as Foods."""
    logging.info("Step 3: Extracting foods...")
    foods = {}  # food_id -> Food
    food_refs = {}  # Original ID -> New Food ID
    
    # Log the menu sections and items we're processing
    sections = menu_data.get("MenuSections", [])
    logging.info(f"Found {len(sections)} menu sections")
    
    # Log the complete structure of the first section for debugging
    if sections:
        logging.info("First section structure:")
        logging.info(json.dumps(sections[0], indent=2))
    
    for section in sections:
        section_id = section.get("MenuSectionId", "unknown")
        section_name = section.get("Name", "unknown")
        items = section.get("MenuItems", [])
        logging.info(f"Processing section {section_id} '{section_name}' with {len(items)} items")
        
        # Log the complete structure of the first item for debugging
        if items:
            logging.info(f"First item in section {section_id} structure:")
            logging.info(json.dumps(items[0], indent=2))
        
        for item in items:
            item_id = item.get("MenuItemId", "unknown")
            item_name = item.get("Name", "unknown")
            logging.info(f"Processing item {item_id} '{item_name}'")
            
            # Log the complete item structure for debugging
            logging.info(f"Item {item_id} structure:")
            logging.info(json.dumps(item, indent=2))
            
            # Create food with string ID
            food = Food(
                _id=str(item.get("MenuItemId") or generate_id()),
                title=(item.get("Name") or "").strip(),
                description=(item.get("Description") or "").strip(),
                image=item.get("ImageUrl"),
                is_active=item.get("IsAvailable", True),
                variations=[]
            )
            
            logging.info(f"Created food {food._id} '{food.title}'")
            
            # Process variations based on addons
            option_sets = item.get("MenuItemOptionSets", [])
            logging.info(f"Found {len(option_sets)} option sets in item {item_id}")
            
            if not option_sets:
                logging.info(f"No option sets found for food {food._id}")
                # No variations, create one default variation
                variation = Variation(
                    _id=generate_id(),
                    title=food.title,
                    price=float(item.get("Price", 0.0)),
                    addons=[]
                )
                food.variations.append(variation)
                logging.info(f"Added default variation for food {food._id}")
            else:
                logging.info(f"Found {len(option_sets)} option sets for food {food._id}")
                # Check for size-based variations
                size_addon = next(
                    (s for s in option_sets 
                    if s.get("Name", "").lower() == "size"),
                    None
                )
                
                if size_addon:
                    logging.info(f"Found size addon for food {food._id}")
                    # Create variations for each size
                    for option in size_addon.get("MenuItemOptionSetItems", []):
                        variation = Variation(
                            _id=generate_id(),
                            title=f"{food.title} - {option.get('Name', '').strip()}",
                            price=float(option.get("Price", item.get("Price", 0.0))),
                            addons=[
                                addon_refs[s["MenuItemOptionSetId"]] 
                                for s in option_sets 
                                if s.get("MenuItemOptionSetId") in addon_refs
                                and s.get("Name", "").lower() != "size"
                            ]
                        )
                        food.variations.append(variation)
                        logging.info(f"Added size variation '{variation.title}' for food {food._id}")
                else:
                    # Create a single variation with all addons
                    variation = Variation(
                        _id=generate_id(),
                        title=food.title,
                        price=float(item.get("Price", 0.0)),
                        addons=[
                            addon_refs[s["MenuItemOptionSetId"]] 
                            for s in option_sets 
                            if s.get("MenuItemOptionSetId") in addon_refs
                        ]
                    )
                    food.variations.append(variation)
                    logging.info(f"Added single variation for food {food._id}")
            
            # Check if we have any variations
            if not food.variations:
                logging.warning(f"No variations created for food {food._id} '{food.title}'")
                # Still add the food even if it has no variations
                foods[food._id] = food
                food_refs[item.get("MenuItemId")] = food._id
                logging.info(f"Added food {food._id} with no variations")
            else:
                logging.info(f"Food {food._id} has {len(food.variations)} variations")
                # Add the food to the dictionary
                foods[food._id] = food
                # Ensure both keys and values are strings for consistency
                food_refs[str(item.get("MenuItemId"))] = str(food._id)
                logging.info(f"Added new food {food._id} to foods dict")
            
            # Deduplicate food by title and variations
            existing_food = next(
                (f for f in foods.values() 
                 if f.title == food.title 
                 and len(f.variations) == len(food.variations)
                 and all(
                     any(
                         v.title == v2.title and v.price == v2.price 
                         for v2 in f.variations
                     ) 
                     for v in food.variations
                 )),
                None
            )
            
            if existing_food:
                logging.info(f"Found duplicate food {food._id} '{food.title}', using existing {existing_food._id}")
                food_refs[item.get("MenuItemId")] = existing_food._id
            else:
                logging.info(f"No duplicate found for food {food._id} '{food.title}'")
    
    logging.info(f"Extracted {len(foods)} unique foods")
    return foods, food_refs
    
    logging.info(f"Extracted {len(foods)} unique foods")
    return foods, food_refs

def extract_categories(menu_data: dict, food_refs: Dict[str, str], all_foods: Dict[str, Food]) -> Dict[str, Category]:
    """Step 4: Extract all MenuSections as Categories."""
    logging.info("Step 4: Extracting categories...")
    categories = {}  # category_id -> Category
    
    # First pass: create all categories and collect food IDs
    for section in menu_data.get("MenuSections", []):
        section_id = section.get("MenuSectionId", "unknown")
        section_name = section.get("Name", "unknown")
        logging.info(f"Processing category {section_id} '{section_name}'")
        
        # Create category
        category = Category(
            _id=section.get("MenuSectionId") or generate_id(),
            title=section.get("Name", "").strip(),
            foods=[]
        )
        
        # Add foods to category
        food_ids = set()
        items = section.get("MenuItems", [])
        logging.info(f"Found {len(items)} items in category {section_id}")
        
        for item in items:
            item_id = item.get("MenuItemId", "unknown")
            logging.info(f"Processing item {item_id} in category {section_id}")
            food_id = food_refs.get(str(item.get("MenuItemId")))
            if food_id:
                logging.info(f"Found food ref for item {item_id}: {food_id}")
                if food_id not in food_ids:
                    food_ids.add(food_id)
                    # Add food to category if it exists in all_foods
                    if str(food_id) in all_foods:
                        food = all_foods[str(food_id)]
                        category.foods.append(food)
                        logging.info(f"Added food {food_id} '{food.title}' to category {section_id}")
                    else:
                        logging.warning(f"Food ID {food_id} not found in all_foods")
            else:
                logging.warning(f"No food ref found for item {item_id}")
        
        logging.info(f"Category {section_id} has {len(category.foods)} foods")
        
        # Deduplicate category by title
        existing_category = next(
            (c for c in categories.values() if c.title == category.title),
            None
        )
        
        if existing_category:
            logging.info(f"Found duplicate category '{category.title}', merging foods")
            # Merge foods from duplicate categories
            existing_food_ids = {f._id for f in existing_category.foods}
            for food in category.foods:
                if food._id not in existing_food_ids:
                    existing_category.foods.append(food)
                    existing_food_ids.add(food._id)
                    logging.info(f"Added food {food._id} to existing category {existing_category._id}")
        else:
            categories[category._id] = category
            logging.info(f"Added new category {category._id} '{category.title}'")
    
    logging.info(f"Extracted {len(categories)} categories")
    return categories
    
    logging.info(f"Extracted {len(categories)} categories")
    return categories

def create_restaurant(menu_data: dict, categories: Dict[str, Category], addons: Dict[str, Addon], options: Dict[str, Option], restaurant_id: str) -> Restaurant:
    """Step 5: Create the final restaurant structure.
    
    Args:
        menu_data: Raw menu data from input JSON
        categories: Dictionary of processed categories
        addons: Dictionary of processed addons
        options: Dictionary of processed options
        restaurant_id: The restaurant ID to use in the output
        
    Returns:
        Restaurant object with all menu data
    """
    logging.info("Step 5: Creating restaurant structure...")
    
    restaurant = Restaurant(
        _id=str(restaurant_id),  # Ensure it's a string
        name=(menu_data.get("Name") or "Restaurant").strip(),
        image=menu_data.get("ImageUrl"),
        categories=list(categories.values()),
        addons=list(addons.values()),
        options=list(options.values())
    )
    
    return restaurant

def write_addon_csv(addons: Dict[str, Addon], options: Dict[str, Option], output_dir: str):
    """Write addon information to CSV file."""
    output_file = Path(output_dir) / "addon.csv"
    with open(output_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        # Write header
        writer.writerow([
            'addon_id', 'addon_title', 'quantity_min', 'quantity_max', 
            'option_id', 'option_title', 'option_price', 'option_description'
        ])
        
        # Write data
        for addon in addons.values():
            for option_id in addon.options:
                option = options.get(option_id, {})
                writer.writerow([
                    addon._id,
                    addon.title,
                    addon.quantity_minimum,
                    addon.quantity_maximum,
                    option_id,
                    getattr(option, 'title', ''),
                    getattr(option, 'price', 0.0),
                    getattr(option, 'description', '')
                ])
    
    logging.info(f"Addon information written to {output_file}")

def write_food_csv(categories: Dict[str, Category], output_dir: str):
    """Write food information to CSV file."""
    output_file = Path(output_dir) / "food.csv"
    
    # Prepare all rows first
    rows = []
    
    # Add header
    header = [
        'category_title', 'food_id', 'food_title', 'food_description', 
        'food_image', 'food_active', 'variation_id', 'variation_title', 
        'variation_price', 'variation_discounted', 'variation_addons'
    ]
    rows.append(header)
    
    # Prepare data rows
    for category in categories.values():
        # Add a row for the category
        category_added = False
        
        for food in category.foods:
            if not food.variations:
                # If no variations, add one row for the food
                row = [
                    category.title,
                    food._id,
                    food.title,
                    food.description or '',
                    food.image or '',
                    'Yes' if food.is_active else 'No',
                    '',  # No variation ID
                    '',  # No variation title
                    '',  # No variation price
                    '',  # No variation discounted
                    ''   # No variation addons
                ]
                rows.append(row)
                category_added = True
            else:
                # Add a row for each variation
                for i, variation in enumerate(food.variations):
                    row = [
                        category.title if i == 0 else '',  # Only show category on first variation
                        food._id if i == 0 else '',  # Only show food ID on first variation
                        food.title if i == 0 else '',  # Only show food title on first variation
                        food.description if i == 0 else '',  # Only show description on first variation
                        food.image if i == 0 else '',  # Only show image on first variation
                        'Yes' if food.is_active and i == 0 else '',  # Only show active status on first variation
                        variation._id,
                        variation.title,
                        str(variation.price) if variation.price is not None else '',
                        str(variation.discounted) if variation.discounted is not None else '',
                        ', '.join(str(a) for a in variation.addons) if variation.addons else ''
                    ]
                    rows.append(row)
                    category_added = True
    
    # Write all rows to CSV
    with open(output_file, 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerows(rows)
    
    logging.info(f"Food information written to {output_file}")

def save_restaurant_json(restaurant: Restaurant, output_file: str):
    """Save the restaurant data to a JSON file."""
    def default_serializer(obj):
        if hasattr(obj, '__dict__'):
            return {k: v for k, v in asdict(obj).items() if v is not None}
        raise TypeError(f"Object of type {obj.__class__.__name__} is not JSON serializable")
    
    output_path = output_file
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(asdict(restaurant), f, indent=2, default=default_serializer, ensure_ascii=False)
    
    logger.info(f"Successfully saved restaurant data to: {os.path.abspath(output_path)}")
    return output_path

def main():
    # Set up argument parser
    parser = argparse.ArgumentParser(description="Restaurant menu restructure tool")
    parser.add_argument("input_file", help="Path to the input JSON file")
    parser.add_argument("restaurant_id", help="Restaurant ID to use in the output")
    
    # Output options
    output_group = parser.add_argument_group('Output options')
    output_group.add_argument("-o", "--output",
                            help="Output file path (overrides --output-dir and --output-filename)")
    output_group.add_argument("--output-dir", 
                            help=f"Output directory (default: {CONFIG.get('output_dir', 'output')})",
                            default=CONFIG.get('output_dir', 'output'))
    output_group.add_argument("--output-filename",
                            help="Output filename (without directory, will be placed in output-dir)")
    
    # Logging options
    log_group = parser.add_argument_group('Logging options')
    log_group.add_argument("--log-dir",
                          help=f"Log directory (default: {CONFIG.get('log_dir', 'logs')})",
                          default=CONFIG.get('log_dir', 'logs'))
    
    args = parser.parse_args()
    
    # Ensure output and log directories exist
    os.makedirs(args.output_dir, exist_ok=True)
    os.makedirs(args.log_dir, exist_ok=True)
    
    # Set up logging
    global logger
    logger = setup_logging(
        log_dir=args.log_dir,
        log_level=CONFIG.get('log_level', 'INFO')
    )
    
    logger.info(f"Starting menu restructuring...")
    logger.info(f"Input file: {os.path.abspath(args.input_file)}")
    logger.info(f"Output directory: {os.path.abspath(args.output_dir)}")
    logger.info(f"Log directory: {os.path.abspath(args.log_dir)}")

    try:
        # Load input data
        with open(args.input_file, 'r', encoding='utf-8') as f:
            menu_data = json.load(f)
        
        # Determine output path based on arguments
        if args.output:
            # Use explicitly specified output path
            output_path = args.output
        else:
            # Generate output filename
            if args.output_filename:
                output_filename = args.output_filename
                if not output_filename.endswith('.json'):
                    output_filename += '.json'
            else:
                # Generate default filename with timestamp
                restaurant_id = str(menu_data.get('_id', 'unknown'))
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                output_filename = f"restaurant_{restaurant_id}_{timestamp}.json"
            
            # Combine with output directory
            output_path = os.path.join(args.output_dir, output_filename)
        
        # Ensure output directory exists
        os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)
        
        logger.info(f"Processing restaurant ID: {menu_data.get('_id', 'unknown')}")
        logger.info(f"Output will be saved to: {os.path.abspath(output_path)}")
        
        # Store output path for later use
        menu_data['_output_path'] = output_path
        
        # Step 1: Extract options with content-based deduplication
        options, option_refs, _ = extract_options(menu_data)
        
        # Step 2: Extract addons with content-based deduplication
        addons, addon_refs, _ = extract_addons(menu_data, option_refs)
        
        # Step 3: Extract foods
        foods, food_refs = extract_foods(menu_data, addon_refs)
        
        # Step 4: Extract categories
        categories = extract_categories(menu_data, food_refs, foods)
        
        # Step 5: Create restaurant
        restaurant = create_restaurant(menu_data, categories, addons, options, args.restaurant_id)
        
        # Save output files
        save_restaurant_json(restaurant, output_path)
        write_addon_csv(addons, options, args.log_dir)
        write_food_csv(categories, args.log_dir)
        
        logging.info("Menu restructuring completed successfully!")
        
    except Exception as e:
        logging.error(f"An error occurred: {str(e)}", exc_info=True)
        raise

if __name__ == '__main__':
    main()
