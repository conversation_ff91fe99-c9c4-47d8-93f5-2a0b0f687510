import json
from typing import Dict, List, Optional, Tuple
from bson import ObjectId
import argparse
import logging  # 导入 logging

# 配置logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def generate_mongodb_objectid() -> str:
    """生成 MongoDB ObjectId 字符串."""
    return str(ObjectId())

# --- 数据结构定义 (根据 restaurant.txt) ---

class Variation:
    def __init__(self, _id: str, title: str, price: float, discounted: Optional[float], addons: List[str]):
        self._id = _id
        self.title = title
        self.price = price
        self.discounted = discounted
        self.addons = addons  # 存储 Addon 的 ID

class Food:
    def __init__(self, _id: str, title: str, description: str, variations: List[Variation], image: Optional[str], is_active: bool):
        self._id = _id
        self.title = title
        self.description = description
        self.variations = variations
        self.image = image
        self.isActive = is_active

class Option:
    def __init__(self, _id: str, title: str, price: float, description:Optional[str]=None):
        self._id = _id
        self.title = title
        self.price = price
        self.description = description #在原始数据中没有对应

    def __eq__(self, other): #Option去重
        if isinstance(other, Option):
            return (self.title == other.title and
                    self.price == other.price and
                    self.description == other.description)
        return False

    def __hash__(self): #Option去重
        return hash((self.title, self.price, self.description))


class Addon:
    def __init__(self, _id: str, title: str, options: List[str], quantity_minimum: int, quantity_maximum: int, description:Optional[str]=None):
        self._id = _id
        self.title = title
        self.options = options  # 存储 Option 的 ID
        self.quantityMinimum = quantity_minimum
        self.quantityMaximum = quantity_maximum
        self.description = description #在原始数据中没有对应

    def __eq__(self, other): #Addon去重
        if isinstance(other, Addon):
            return (self.title == other.title and
                    set(self.options) == set(other.options) and # 忽略options顺序
                    self.quantityMinimum == other.quantityMinimum and
                    self.quantityMaximum == other.quantityMaximum and
                    self.description == other.description)
        return False

    def __hash__(self): #Addon去重
        return hash((self.title, tuple(sorted(self.options)), self.quantityMinimum, self.quantityMaximum, self.description))


class Category:
    def __init__(self, _id: str, title: str, foods: List[Food]):
        self._id = _id
        self.title = title
        self.foods = foods

class Restaurant:  # Simplified Restaurant for this task
     def __init__(self, _id: str, name: str, image:Optional[str]=None):
        self._id = _id
        self.name = name
        self.image = image
        self.categories: List[Category] = [] # 简化, 专注menu部分


def _convert_option(item_data: Dict) -> Option:
    """转换 MenuItemOptionSetItem 到 Option."""
    option_id = str(item_data.get("MenuItemOptionSetItemId", generate_mongodb_objectid()))
    return Option(
        _id=option_id,
        title=item_data.get("Name", "Unnamed Option"),
        price=item_data.get("Price", 0.0),
        description=item_data.get("Description")
    )


def _convert_addon(option_set_data: Dict, option_map: Dict[Option, str]) -> Tuple[Addon, Dict[Option, str]]:
    """转换 MenuItemOptionSet 到 Addon."""
    addon_id = option_set_data.get('PublicId') or generate_mongodb_objectid()
    addon = Addon(
        _id=addon_id,
        title=option_set_data.get("Name", "Unnamed Addon"),
        options=[],
        quantity_minimum=option_set_data.get("MinSelectCount", 0),
        quantity_maximum=option_set_data.get("MaxSelectCount", 0),
        description=option_set_data.get("Description", "")
    )
    
    for item_data in option_set_data.get("MenuItemOptionSetItems", []):
        option_obj = _convert_option(item_data)
        found_option = next((ex_option for ex_option in option_map.keys() if ex_option == option_obj), None)
        if found_option:
            option_id_to_use = option_map[found_option]
        else:
            option_id_to_use = option_obj._id
            option_map[option_obj] = option_id_to_use
        addon.options.append(option_id_to_use)

    return addon, option_map


def _convert_variation(option_set_data: Dict, option_map: Dict[Option, str], addon_map: Dict[Addon, str], menu_item_option_sets_len:int, menu_item:Dict) -> Tuple[Variation, Dict[Option, str], Dict[Addon, str]]:
    """转换 MenuItemOptionSet 到 Variation."""
    variation_id = option_set_data.get('PublicId') or generate_mongodb_objectid()
    if menu_item_option_sets_len <= 1:
        base_price = menu_item.get("Price", 0.0)
    else:
        base_price = 0.0
    variation = Variation(
        _id=variation_id,
        title=option_set_data.get("Name", "Unnamed Variation"),
        price=base_price,
        discounted=None,
        addons=[]
    )

    for item_data in option_set_data.get("MenuItemOptionSetItems", []):
        option_obj = _convert_option(item_data)
        found_option = next((ex_option for ex_option in option_map.keys() if ex_option == option_obj), None)
        option_id_to_use = option_map[found_option] if found_option else option_obj._id
        variation.price += item_data.get("Price", 0.0)
        variation.addons.append(option_id_to_use)

        next_option_set_id = item_data.get("NextMenuItemOptionSetId")
        if next_option_set_id is not None and str(next_option_set_id) not in variation.addons:
            variation.addons.append(str(next_option_set_id))

    return variation, option_map, addon_map



def _convert_food(menu_item_data: Dict, option_map: Dict[Option, str], addon_map: Dict[Addon, str]) -> Tuple[Food, Dict[Option, str], Dict[Addon, str]]:
    """转换 MenuSectionItem 到 Food."""
    food_id = str(menu_item_data.get("MenuItemId", generate_mongodb_objectid()))
    food = Food(
        _id=food_id,
        title=menu_item_data.get("Name", "Unnamed Food"),
        description=menu_item_data.get("Description", ""),
        image=menu_item_data.get("ImageUrl"),
        is_active=menu_item_data.get("IsAvailable", False),
        variations=[]
    )

    menu_item_option_sets = menu_item_data.get("MenuItemOptionSets", [])
    for option_set_data in menu_item_option_sets:
        if option_set_data.get("IsMasterOptionSet"): #IsMasterOptionSet不再是判断variation/addon的条件
            variation, option_map, addon_map = _convert_variation(option_set_data, option_map, addon_map, len(menu_item_option_sets), menu_item_data)
            food.variations.append(variation)
        else:
            addon, option_map = _convert_addon(option_set_data, option_map)
            addon_map[addon] = addon._id #ADDON_MAP 存储 Addon 对象到 _id 的映射
            variation = Variation(_id=addon._id, title=addon.title, price=0.0, discounted=None, addons=addon.options) #addon也作为一个variation
            food.variations.append(variation)

    return food, option_map, addon_map


def _convert_category(menu_section_data: Dict, option_map: Dict[Option, str], addon_map: Dict[Addon, str]) -> Tuple[Category, Dict[Option, str], Dict[Addon, str]]:
    """转换 MenuSection 到 Category."""
    category_id = str(menu_section_data.get("MenuSectionId", generate_mongodb_objectid()))
    category = Category(
        _id=category_id,
        title=menu_section_data.get("Name", "Unnamed Category"),
        foods=[]
    )
    
    for menu_item_data in menu_section_data.get("MenuItems", []):
        food, option_map, addon_map = _convert_food(menu_item_data, option_map, addon_map)
        category.foods.append(food)

    return category, option_map, addon_map


def convert_menu(menu_data: dict) -> Tuple[Restaurant, Dict[Option, str], Dict[Addon, str]]:
    """主函数：将原始菜单数据转换为目标数据结构."""
    logging.info("开始转换菜单数据...")

    restaurant = Restaurant(
        _id=str(menu_data.get("MenuId", generate_mongodb_objectid())),
        name=menu_data.get("Name", "Unnamed Menu"),
        image=menu_data.get("ImageUrl")
    )
    option_map: Dict[Option, str] = {}
    addon_map: Dict[Addon, str] = {}

    for menu_section_data in menu_data.get("MenuSections", []):
        category, option_map, addon_map = _convert_category(menu_section_data, option_map, addon_map)
        restaurant.categories.append(category)

    logging.info("菜单数据转换完成。")
    return restaurant, option_map, addon_map



# --- 主程序 ---

if __name__ == "__main__":
     # --- 使用 argparse 获取输入输出文件 ---
    parser = argparse.ArgumentParser(description="Convert menu data to the target structure.")
    parser.add_argument("input_file", help="Path to the input JSON file.")
    parser.add_argument("output_file", help="Path to the output JSON file.")
    args = parser.parse_args()
    input_file = args.input_file
    output_file = args.output_file


    try:
        # --- 加载 JSON 数据 ---
        with open(input_file, "r", encoding="utf-8") as f:
            menu_data = json.load(f)

        # --- 执行转换 ---
        restaurant, option_map, addon_map = convert_menu(menu_data) #接收返回值,  option_map, addon_map 也要返回

        # --- 输出结果 (示例：输出为 JSON) ---
        def serialize_restaurant(restaurant: Restaurant, option_map: Dict[Option, str], addon_map: Dict[Addon, str]):
            option_id_map_reversed = {v: k for k, v in option_map.items()}
            addon_id_map_reversed = {v: k for k, v in addon_map.items()}

            return {
                "_id": restaurant._id,
                "name": restaurant.name,
                "image": restaurant.image,
                "categories": [
                    {
                        "_id": category._id,
                        "title": category.title,
                        "foods": [
                            {
                                "_id": food._id,
                                "title": food.title,
                                "description": food.description,
                                "image": food.image,
                                "isActive": food.isActive,
                                "variations": [
                                    {
                                        "_id": variation._id,
                                        "title": variation.title,
                                        "price": variation.price,
                                        "discounted": variation.discounted,
                                        "addons": variation.addons,
                                    } for variation in food.variations
                                ],
                            } for food in category.foods
                        ]
                    } for category in restaurant.categories
               ],
               "options": [
                   {
                       "_id": option_id,
                       "title": option_obj.title,
                       "price": option_obj.price,
                       "description": option_obj.description
                   } for option_id, option_obj in option_id_map_reversed.items()
               ],
                "addons": [
                    {
                        "_id": addon_id,
                        "title": addon_obj.title,
                        "options": addon_obj.options,
                        "quantityMinimum": addon_obj.quantityMinimum,
                        "quantityMaximum": addon_obj.quantityMaximum,
                        "description": addon_obj.description,
                    } for addon_id, addon_obj in addon_id_map_reversed.items()
                ]
            }

        with open(output_file, "w", encoding="utf-8") as outfile:
            json.dump(serialize_restaurant(restaurant, option_map, addon_map), outfile, indent=2, ensure_ascii=False)

        logging.info(f"转换后的菜单已成功保存到: {output_file}")
        print("\n--- 原始数据有, 目标结构没有, 需要提取的字段 ---")
        print("TaxRateName, TaxRateId, TaxValue, ExcludeFromVoucherDiscounting")


    except FileNotFoundError:
        logging.error(f"错误: 输入文件 '{input_file}' 未找到.", exc_info=True) # 记录更详细的错误信息
        print(f"错误: 输入文件 '{input_file}' 未找到.")
    except json.JSONDecodeError:
        logging.error(f"错误: JSON 格式解码错误，请检查文件 '{input_file}' 的格式是否正确.", exc_info=True) # 记录更详细的错误信息
        print(f"错误: JSON 格式解码错误，请检查文件 '{input_file}' 的格式是否正确.")
    except Exception as e:
        logging.error(f"发生未知错误: {e}", exc_info=True) # 记录更详细的错误信息
        print(f"发生未知错误，请查看日志文件获取详细信息。")