// test/data/restaurant_sushi_spot.json
{
  "_id": "restaurant_id_sushi_spot",
  "name": "Sushi Spot",
  "options": [
    {
      "_id": "opt_nigiri_2pcs",
      "title": "2 Pieces",
      "price": 0.0
    },
    {
      "_id": "opt_nigiri_4pcs",
      "title": "4 Pieces",
      "price": 4.00 
    },
    {
      "_id": "opt_soysauce_regular",
      "title": "Regular Soy Sauce",
      "price": 0.0
    },
    {
      "_id": "opt_soysauce_low_sodium",
      "title": "Low Sodium Soy Sauce",
      "price": 0.10
    },
    {
      "_id": "opt_wasabi_yes",
      "title": "With Wasabi",
      "price": 0.0
    },
    {
      "_id": "opt_wasabi_no",
      "title": "No Wasabi",
      "price": 0.0
    },
    {
      "_id": "opt_extra_ginger_yes",
      "title": "Yes",
      "price": 0.50
    },
    {
      "_id": "opt_miso_soup_yes",
      "title": "Yes",
      "price": 2.00
    }
  ],
  "addons": [
    {
      "_id": "addon_soy_choice",
      "title": "Soy Sauce Choice",
      "description": "Select your soy sauce preference",
      "options": ["opt_soysauce_regular", "opt_soysauce_low_sodium"],
      "quantity_minimum": 1,
      "quantity_maximum": 1
    },
    {
      "_id": "addon_wasabi_option",
      "title": "Wasabi",
      "description": "Include wasabi?",
      "options": ["opt_wasabi_yes", "opt_wasabi_no"],
      "quantity_minimum": 1,
      "quantity_maximum": 1
    },
    {
      "_id": "addon_extra_ginger",
      "title": "Extra Ginger",
      "options": ["opt_extra_ginger_yes"],
      "quantity_minimum": 0,
      "quantity_maximum": 1
    },
    {
      "_id": "addon_miso_soup_side",
      "title": "Add Miso Soup",
      "options": ["opt_miso_soup_yes"],
      "quantity_minimum": 0,
      "quantity_maximum": 1
    }
  ],
  "categories": [
    {
      "_id": "cat_nigiri_sushi",
      "title": "Nigiri Sushi",
      "foods": [
        {
          "_id": "food_salmon_nigiri",
          "title": "Salmon Nigiri",
          "description": "Fresh salmon over pressed vinegar rice.",
          "image": "http://example.com/images/salmon_nigiri.jpg",
          "is_active": true,
          "variations": [
            {
              "_id": "var_salmon_nigiri_2pcs",
              "title": "2 Pieces",
              "price": 5.00,
              "addons": ["addon_soy_choice", "addon_wasabi_option", "addon_extra_ginger", "addon_miso_soup_side"]
            },
            {
              "_id": "var_salmon_nigiri_4pcs",
              "title": "4 Pieces",
              "price": 9.50,
              "addons": ["addon_soy_choice", "addon_wasabi_option", "addon_extra_ginger", "addon_miso_soup_side"]
            }
          ]
        },
        {
          "_id": "food_tuna_nigiri",
          "title": "Tuna (Maguro) Nigiri",
          "description": "Prime tuna over pressed vinegar rice.",
          "image": "http://example.com/images/tuna_nigiri.jpg",
          "is_active": true,
          "variations": [
            {
              "_id": "var_tuna_nigiri_2pcs",
              "title": "2 Pieces",
              "price": 6.00,
              "addons": ["addon_soy_choice", "addon_wasabi_option", "addon_extra_ginger"]
            },
            {
              "_id": "var_tuna_nigiri_4pcs",
              "title": "4 Pieces",
              "price": 11.00,
              "discounted": 10.50,
              "addons": ["addon_soy_choice", "addon_wasabi_option", "addon_extra_ginger"]
            }
          ]
        }
      ]
    },
    {
      "_id": "cat_maki_rolls",
      "title": "Maki Rolls (Cut Rolls)",
      "foods": [
        {
          "_id": "food_california_roll",
          "title": "California Roll",
          "description": "Crab, avocado, and cucumber rolled in seaweed and rice.",
          "is_active": true,
          "variations": [
            {
              "_id": "var_california_roll_std",
              "title": "Standard (8 pieces)",
              "price": 7.50,
              "addons": ["addon_soy_choice", "addon_wasabi_option", "addon_extra_ginger", "addon_miso_soup_side"]
            }
          ]
        },
        {
          "_id": "food_spicy_tuna_roll",
          "title": "Spicy Tuna Roll",
          "description": "Spicy tuna mix with cucumber.",
          "is_active": false,
          "variations": [
            {
              "_id": "var_spicy_tuna_roll_std",
              "title": "Standard (8 pieces)",
              "price": 8.00,
              "addons": ["addon_soy_choice", "addon_wasabi_option", "addon_extra_ginger"]
            }
          ]
        }
      ]
    },
    {
      "_id": "cat_appetizers_sushi",
      "title": "Appetizers",
      "foods": [
        {
          "_id": "food_edamame",
          "title": "Edamame",
          "description": "Steamed soybeans with sea salt.",
          "is_active": true,
          "variations": [
            {
              "_id": "var_edamame_regular",
              "title": "Regular",
              "price": 4.00,
              "addons": []
            }
          ]
        }
      ]
    }
  ]
}