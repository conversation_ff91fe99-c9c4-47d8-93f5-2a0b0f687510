/* tslint:disable */
/* eslint-disable */
/**
 * Flipdish Open API v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { BusinessHoursPeriod } from './business-hours-period';
// May contain unused imports in some cases
// @ts-ignore
import type { CreateMetadata } from './create-metadata';
// May contain unused imports in some cases
// @ts-ignore
import type { MenuItemOptionSet } from './menu-item-option-set';

/**
 * Menu item
 * @export
 * @interface MenuSectionItem
 */
export interface MenuSectionItem {
    /**
     * Actual price - the minimum price possible when all required option set items prices are included.
     * @type {number}
     * @memberof MenuSectionItem
     */
    'ActualPrice'?: number;
    /**
     * Menu item option sets
     * @type {Array<MenuItemOptionSet>}
     * @memberof MenuSectionItem
     */
    'MenuItemOptionSets'?: Array<MenuItemOptionSet>;
    /**
     * Daily special hours
     * @type {Array<BusinessHoursPeriod>}
     * @memberof MenuSectionItem
     */
    'DailySpecialHours'?: Array<BusinessHoursPeriod>;
    /**
     * Permanent reference to the item.
     * @type {string}
     * @memberof MenuSectionItem
     */
    'PublicId'?: string;
    /**
     * Tax rate name
     * @type {string}
     * @memberof MenuSectionItem
     */
    'TaxRateName'?: string;
    /**
     * TaxRate
     * @type {number}
     * @memberof MenuSectionItem
     */
    'TaxRateId'?: number | null;
    /**
     * TaxValue - the tax associated with this item, based on TaxRate / TaxType and Currency (currency determines decimal point precision)
     * @type {number}
     * @memberof MenuSectionItem
     */
    'TaxValue'?: number;
    /**
     * If true, the item is excluded from voucher discount calculations
     * @type {boolean}
     * @memberof MenuSectionItem
     */
    'ExcludeFromVoucherDiscounting'?: boolean;
    /**
     * List of metadata
     * @type {Array<CreateMetadata>}
     * @memberof MenuSectionItem
     */
    'Metadata'?: Array<CreateMetadata>;
    /**
     * Catalog item Id when the Item is associated to a Product
     * @type {string}
     * @memberof MenuSectionItem
     */
    'CatalogItemId'?: string;
    /**
     * Menu item name (like \"Korma\")
     * @type {string}
     * @memberof MenuSectionItem
     */
    'Name'?: string;
    /**
     * Description (like \"A lovely dish from the east\")
     * @type {string}
     * @memberof MenuSectionItem
     */
    'Description'?: string;
    /**
     * Spiciness rating
     * @type {string}
     * @memberof MenuSectionItem
     */
    'SpicinessRating'?: MenuSectionItemSpicinessRatingEnum;
    /**
     * Price - this is only used when there is no master option set and should be set to 0 if a master option set exists.
     * @type {number}
     * @memberof MenuSectionItem
     */
    'Price'?: number;
    /**
     * An optional fee that can be added to the price of the item.
     * @type {number}
     * @memberof MenuSectionItem
     */
    'DepositReturnFee'?: number | null;
    /**
     * Display order
     * @type {number}
     * @memberof MenuSectionItem
     */
    'DisplayOrder'?: number;
    /**
     * To be set true if the item or an option of the item contains an alcoholic drink.
     * @type {boolean}
     * @memberof MenuSectionItem
     */
    'Alcohol'?: boolean;
    /**
     * True if we accept orders for this item still
     * @type {boolean}
     * @memberof MenuSectionItem
     */
    'IsAvailable'?: boolean;
    /**
     * Small | Medium | Large  Affects the layout of the menu.
     * @type {string}
     * @memberof MenuSectionItem
     */
    'CellLayoutType'?: MenuSectionItemCellLayoutTypeEnum;
    /**
     * If true, then vouchers won\'t be applied for this item
     * @type {boolean}
     * @memberof MenuSectionItem
     */
    'DisableVouchers'?: boolean;
    /**
     * Image url
     * @type {string}
     * @memberof MenuSectionItem
     */
    'ImageName'?: string;
    /**
     * Image url
     * @type {string}
     * @memberof MenuSectionItem
     */
    'ImageUrl'?: string;
    /**
     * Menu Item Id
     * @type {number}
     * @memberof MenuSectionItem
     */
    'MenuItemId'?: number;
}

export const MenuSectionItemSpicinessRatingEnum = {
    NotRated: 'NotRated',
    Mild: 'Mild',
    Medium: 'Medium',
    Hot: 'Hot'
} as const;

export type MenuSectionItemSpicinessRatingEnum = typeof MenuSectionItemSpicinessRatingEnum[keyof typeof MenuSectionItemSpicinessRatingEnum];
export const MenuSectionItemCellLayoutTypeEnum = {
    Small: 'Small',
    Medium: 'Medium',
    Large: 'Large',
    HiddenImage: 'HiddenImage'
} as const;

export type MenuSectionItemCellLayoutTypeEnum = typeof MenuSectionItemCellLayoutTypeEnum[keyof typeof MenuSectionItemCellLayoutTypeEnum];


