/* tslint:disable */
/* eslint-disable */
/**
 * Flipdish Open API v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { CreateMetadata } from './create-metadata';

/**
 * Menu item option set item
 * @export
 * @interface MenuItemOptionSetItem
 */
export interface MenuItemOptionSetItem {
    /**
     * Menu item option set item identifier
     * @type {number}
     * @memberof MenuItemOptionSetItem
     */
    'MenuItemOptionSetItemId'?: number;
    /**
     * Permanent reference to the item.
     * @type {string}
     * @memberof MenuItemOptionSetItem
     */
    'PublicId'?: string;
    /**
     * List of metadata
     * @type {Array<CreateMetadata>}
     * @memberof MenuItemOptionSetItem
     */
    'Metadata'?: Array<CreateMetadata>;
    /**
     * if null, next option set is next. if -1, this is the final option set
     * @type {number}
     * @memberof MenuItemOptionSetItem
     */
    'NextMenuItemOptionSetId'?: number | null;
    /**
     * Catalog item Id when the OptionSet is associated to a Product
     * @type {string}
     * @memberof MenuItemOptionSetItem
     */
    'CatalogItemId'?: string;
    /**
     * Tax rate name
     * @type {string}
     * @memberof MenuItemOptionSetItem
     */
    'TaxRateName'?: string;
    /**
     * 
     * @type {number}
     * @memberof MenuItemOptionSetItem
     */
    'TaxRateId'?: number | null;
    /**
     * 
     * @type {number}
     * @memberof MenuItemOptionSetItem
     */
    'TaxValue'?: number;
    /**
     * Name
     * @type {string}
     * @memberof MenuItemOptionSetItem
     */
    'Name'?: string;
    /**
     * Price
     * @type {number}
     * @memberof MenuItemOptionSetItem
     */
    'Price'?: number;
    /**
     * An optional fee that can be added to the price of the item.
     * @type {number}
     * @memberof MenuItemOptionSetItem
     */
    'DepositReturnFee'?: number | null;
    /**
     * Is available
     * @type {boolean}
     * @memberof MenuItemOptionSetItem
     */
    'IsAvailable'?: boolean;
    /**
     * Display order. Displayed in ascending order.
     * @type {number}
     * @memberof MenuItemOptionSetItem
     */
    'DisplayOrder'?: number;
    /**
     * Small | Medium | Large  Affects the layout of the menu.
     * @type {string}
     * @memberof MenuItemOptionSetItem
     */
    'CellLayoutType'?: MenuItemOptionSetItemCellLayoutTypeEnum;
    /**
     * Image url
     * @type {string}
     * @memberof MenuItemOptionSetItem
     */
    'ImageUrl'?: string;
}

export const MenuItemOptionSetItemCellLayoutTypeEnum = {
    Small: 'Small',
    Medium: 'Medium',
    Large: 'Large',
    HiddenImage: 'HiddenImage'
} as const;

export type MenuItemOptionSetItemCellLayoutTypeEnum = typeof MenuItemOptionSetItemCellLayoutTypeEnum[keyof typeof MenuItemOptionSetItemCellLayoutTypeEnum];


