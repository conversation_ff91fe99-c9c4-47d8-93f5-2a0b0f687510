/* tslint:disable */
/* eslint-disable */
/**
 * Flipdish Open API v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { Range } from './range';

/**
 * Business hours period
 * @export
 * @interface BusinessHoursPeriod
 */
export interface BusinessHoursPeriod {
    /**
     * 
     * @type {Range}
     * @memberof BusinessHoursPeriod
     */
    'Early'?: Range;
    /**
     * 
     * @type {Range}
     * @memberof BusinessHoursPeriod
     */
    'Late'?: Range;
    /**
     * Ranges
     * @type {Array<Range>}
     * @memberof BusinessHoursPeriod
     */
    'Ranges'?: Array<Range>;
    /**
     * Day of week
     * @type {string}
     * @memberof BusinessHoursPeriod
     */
    'DayOfWeek'?: BusinessHoursPeriodDayOfWeekEnum;
    /**
     * Start time
     * @type {string}
     * @memberof BusinessHoursPeriod
     */
    'StartTime'?: string;
    /**
     * Period
     * @type {string}
     * @memberof BusinessHoursPeriod
     */
    'Period'?: string;
    /**
     * Start time early
     * @type {string}
     * @memberof BusinessHoursPeriod
     */
    'StartTimeEarly'?: string;
    /**
     * Period early
     * @type {string}
     * @memberof BusinessHoursPeriod
     */
    'PeriodEarly'?: string;
}

export const BusinessHoursPeriodDayOfWeekEnum = {
    Sunday: 'Sunday',
    Monday: 'Monday',
    Tuesday: 'Tuesday',
    Wednesday: 'Wednesday',
    Thursday: 'Thursday',
    Friday: 'Friday',
    Saturday: 'Saturday'
} as const;

export type BusinessHoursPeriodDayOfWeekEnum = typeof BusinessHoursPeriodDayOfWeekEnum[keyof typeof BusinessHoursPeriodDayOfWeekEnum];


