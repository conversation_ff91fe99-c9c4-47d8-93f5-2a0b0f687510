/* tslint:disable */
/* eslint-disable */
/**
 * Flipdish Open API v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { BusinessHoursPeriod } from './business-hours-period';

/**
 * Menu section availability
 * @export
 * @interface MenuSectionAvailability
 */
export interface MenuSectionAvailability {
    /**
     * Available times
     * @type {Array<BusinessHoursPeriod>}
     * @memberof MenuSectionAvailability
     */
    'AvailableTimes'?: Array<BusinessHoursPeriod>;
    /**
     * Availability mode
     * @type {string}
     * @memberof MenuSectionAvailability
     */
    'AvailabilityMode'?: MenuSectionAvailabilityAvailabilityModeEnum | null;
}

export const MenuSectionAvailabilityAvailabilityModeEnum = {
    DisplayAlways: 'DisplayAlways',
    DisplayBasedOnTimes: 'DisplayBasedOnTimes',
    DisplayAlwaysStartCollapsed: 'DisplayAlwaysStartCollapsed',
    DisplayAlwaysStartCollapsedBasedOnTimes: 'DisplayAlwaysStartCollapsedBasedOnTimes'
} as const;

export type MenuSectionAvailabilityAvailabilityModeEnum = typeof MenuSectionAvailabilityAvailabilityModeEnum[keyof typeof MenuSectionAvailabilityAvailabilityModeEnum];


