/* tslint:disable */
/* eslint-disable */
/**
 * Flipdish Open API v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { Range } from './range';

/**
 * Business hours period
 * @export
 * @interface BusinessHoursPeriod
 */
export interface BusinessHoursPeriod {
    /**
     * 
     * @type {Range}
     * @memberof BusinessHoursPeriod
     */
    'Early'?: Range;
    /**
     * 
     * @type {Range}
     * @memberof BusinessHoursPeriod
     */
    'Late'?: Range;
    /**
     * Ranges
     * @type {Array<Range>}
     * @memberof BusinessHoursPeriod
     */
    'Ranges'?: Array<Range>;
    /**
     * Day of week
     * @type {string}
     * @memberof BusinessHoursPeriod
     */
    'DayOfWeek'?: BusinessHoursPeriodDayOfWeekEnum;
    /**
     * Start time
     * @type {string}
     * @memberof BusinessHoursPeriod
     */
    'StartTime'?: string;
    /**
     * Period
     * @type {string}
     * @memberof BusinessHoursPeriod
     */
    'Period'?: string;
    /**
     * Start time early
     * @type {string}
     * @memberof BusinessHoursPeriod
     */
    'StartTimeEarly'?: string;
    /**
     * Period early
     * @type {string}
     * @memberof BusinessHoursPeriod
     */
    'PeriodEarly'?: string;
}

export const BusinessHoursPeriodDayOfWeekEnum = {
    Sunday: 'Sunday',
    Monday: 'Monday',
    Tuesday: 'Tuesday',
    Wednesday: 'Wednesday',
    Thursday: 'Thursday',
    Friday: 'Friday',
    Saturday: 'Saturday'
} as const;

export type BusinessHoursPeriodDayOfWeekEnum = typeof BusinessHoursPeriodDayOfWeekEnum[keyof typeof BusinessHoursPeriodDayOfWeekEnum];


/* tslint:disable */
/* eslint-disable */
/**
 * Flipdish Open API v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { CreateMetadata } from './create-metadata';

/**
 * Menu item option set item
 * @export
 * @interface MenuItemOptionSetItem
 */
export interface MenuItemOptionSetItem {
    /**
     * Menu item option set item identifier
     * @type {number}
     * @memberof MenuItemOptionSetItem
     */
    'MenuItemOptionSetItemId'?: number;
    /**
     * Permanent reference to the item.
     * @type {string}
     * @memberof MenuItemOptionSetItem
     */
    'PublicId'?: string;
    /**
     * List of metadata
     * @type {Array<CreateMetadata>}
     * @memberof MenuItemOptionSetItem
     */
    'Metadata'?: Array<CreateMetadata>;
    /**
     * if null, next option set is next. if -1, this is the final option set
     * @type {number}
     * @memberof MenuItemOptionSetItem
     */
    'NextMenuItemOptionSetId'?: number | null;
    /**
     * Catalog item Id when the OptionSet is associated to a Product
     * @type {string}
     * @memberof MenuItemOptionSetItem
     */
    'CatalogItemId'?: string;
    /**
     * Tax rate name
     * @type {string}
     * @memberof MenuItemOptionSetItem
     */
    'TaxRateName'?: string;
    /**
     * 
     * @type {number}
     * @memberof MenuItemOptionSetItem
     */
    'TaxRateId'?: number | null;
    /**
     * 
     * @type {number}
     * @memberof MenuItemOptionSetItem
     */
    'TaxValue'?: number;
    /**
     * Name
     * @type {string}
     * @memberof MenuItemOptionSetItem
     */
    'Name'?: string;
    /**
     * Price
     * @type {number}
     * @memberof MenuItemOptionSetItem
     */
    'Price'?: number;
    /**
     * An optional fee that can be added to the price of the item.
     * @type {number}
     * @memberof MenuItemOptionSetItem
     */
    'DepositReturnFee'?: number | null;
    /**
     * Is available
     * @type {boolean}
     * @memberof MenuItemOptionSetItem
     */
    'IsAvailable'?: boolean;
    /**
     * Display order. Displayed in ascending order.
     * @type {number}
     * @memberof MenuItemOptionSetItem
     */
    'DisplayOrder'?: number;
    /**
     * Small | Medium | Large  Affects the layout of the menu.
     * @type {string}
     * @memberof MenuItemOptionSetItem
     */
    'CellLayoutType'?: MenuItemOptionSetItemCellLayoutTypeEnum;
    /**
     * Image url
     * @type {string}
     * @memberof MenuItemOptionSetItem
     */
    'ImageUrl'?: string;
}

export const MenuItemOptionSetItemCellLayoutTypeEnum = {
    Small: 'Small',
    Medium: 'Medium',
    Large: 'Large',
    HiddenImage: 'HiddenImage'
} as const;

export type MenuItemOptionSetItemCellLayoutTypeEnum = typeof MenuItemOptionSetItemCellLayoutTypeEnum[keyof typeof MenuItemOptionSetItemCellLayoutTypeEnum];


/* tslint:disable */
/* eslint-disable */
/**
 * Flipdish Open API v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { MenuItemOptionSetItem } from './menu-item-option-set-item';

/**
 * Menu item option set
 * @export
 * @interface MenuItemOptionSet
 */
export interface MenuItemOptionSet {
    /**
     * Menu item option set identifier
     * @type {number}
     * @memberof MenuItemOptionSet
     */
    'MenuItemOptionSetId'?: number;
    /**
     * Image Name
     * @type {string}
     * @memberof MenuItemOptionSet
     */
    'ImageName'?: string;
    /**
     * Image url
     * @type {string}
     * @memberof MenuItemOptionSet
     */
    'ImageUrl'?: string;
    /**
     * Option set items
     * @type {Array<MenuItemOptionSetItem>}
     * @memberof MenuItemOptionSet
     */
    'MenuItemOptionSetItems'?: Array<MenuItemOptionSetItem>;
    /**
     * Permanent reference to the item.
     * @type {string}
     * @memberof MenuItemOptionSet
     */
    'PublicId'?: string;
    /**
     * Catalog item Id when the OptionSet is associated to a Product
     * @type {string}
     * @memberof MenuItemOptionSet
     */
    'CatalogItemId'?: string;
    /**
     * Menu item option set name
     * @type {string}
     * @memberof MenuItemOptionSet
     */
    'Name'?: string;
    /**
     * Is master option set. This can affect the layout of the options in the menu displayed to the customer. Usually it is true if the option could be considerd a standalone item as opposed to an addition (\"with ketchup\") or modifier (\"large\").
     * @type {boolean}
     * @memberof MenuItemOptionSet
     */
    'IsMasterOptionSet'?: boolean;
    /**
     * Display order. Displayed in ascending order.
     * @type {number}
     * @memberof MenuItemOptionSet
     */
    'DisplayOrder'?: number;
    /**
     * Minimum items must be selected
     * @type {number}
     * @memberof MenuItemOptionSet
     */
    'MinSelectCount'?: number;
    /**
     * Maximum number of items can be selected
     * @type {number}
     * @memberof MenuItemOptionSet
     */
    'MaxSelectCount'?: number;
    /**
     * Small | Medium | Large  Affects the layout of the menu.
     * @type {string}
     * @memberof MenuItemOptionSet
     */
    'CellLayoutType'?: MenuItemOptionSetCellLayoutTypeEnum;
}

export const MenuItemOptionSetCellLayoutTypeEnum = {
    Small: 'Small',
    Medium: 'Medium',
    Large: 'Large',
    HiddenImage: 'HiddenImage'
} as const;

export type MenuItemOptionSetCellLayoutTypeEnum = typeof MenuItemOptionSetCellLayoutTypeEnum[keyof typeof MenuItemOptionSetCellLayoutTypeEnum];


/* tslint:disable */
/* eslint-disable */
/**
 * Flipdish Open API v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { MenuSection } from './menu-section';
// May contain unused imports in some cases
// @ts-ignore
import type { MenuTaxRate } from './menu-tax-rate';

/**
 * Menu
 * @export
 * @interface Menu
 */
export interface Menu {
    /**
     * Menu identifier
     * @type {number}
     * @memberof Menu
     */
    'MenuId'?: number;
    /**
     * Last modified time
     * @type {string}
     * @memberof Menu
     */
    'ModifiedTime'?: string;
    /**
     * Menu version
     * @type {number}
     * @memberof Menu
     */
    'VersionNumber'?: number;
    /**
     * Image Name
     * @type {string}
     * @memberof Menu
     */
    'ImageName'?: string;
    /**
     * Image url
     * @type {string}
     * @memberof Menu
     */
    'ImageUrl'?: string;
    /**
     * Name of Menu, only shown in portal
     * @type {string}
     * @memberof Menu
     */
    'Name'?: string;
    /**
     * Locked: is menu locked against modifcation
     * @type {boolean}
     * @memberof Menu
     */
    'Locked'?: boolean;
    /**
     * Menu sections (startes, main etc)
     * @type {Array<MenuSection>}
     * @memberof Menu
     */
    'MenuSections'?: Array<MenuSection>;
    /**
     * Menu tax rates
     * @type {Array<MenuTaxRate>}
     * @memberof Menu
     */
    'TaxRates'?: Array<MenuTaxRate>;
    /**
     * Identifier of App the menu is attached to
     * @type {string}
     * @memberof Menu
     */
    'AppId'?: string;
    /**
     * Display menu section link on UI
     * @type {boolean}
     * @memberof Menu
     */
    'DisplaySectionLinks'?: boolean;
    /**
     * Menu section behaviour
     * @type {string}
     * @memberof Menu
     */
    'MenuSectionBehaviour'?: MenuMenuSectionBehaviourEnum;
    /**
     * Tax type
     * @type {string}
     * @memberof Menu
     */
    'TaxType'?: MenuTaxTypeEnum;
    /**
     * Flag to indicate if the menu is integrated (contains metadata)
     * @type {boolean}
     * @memberof Menu
     */
    'IsIntegrated'?: boolean;
}

export const MenuMenuSectionBehaviourEnum = {
    ExpandSingle: 'ExpandSingle',
    ExpandMultiple: 'ExpandMultiple'
} as const;

export type MenuMenuSectionBehaviourEnum = typeof MenuMenuSectionBehaviourEnum[keyof typeof MenuMenuSectionBehaviourEnum];
export const MenuTaxTypeEnum = {
    IncludedInBasePrice: 'IncludedInBasePrice',
    ExcludedFromBasePrice: 'ExcludedFromBasePrice'
} as const;

export type MenuTaxTypeEnum = typeof MenuTaxTypeEnum[keyof typeof MenuTaxTypeEnum];


/* tslint:disable */
/* eslint-disable */
/**
 * Flipdish Open API v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { BusinessHoursPeriod } from './business-hours-period';

/**
 * Menu section availability
 * @export
 * @interface MenuSectionAvailability
 */
export interface MenuSectionAvailability {
    /**
     * Available times
     * @type {Array<BusinessHoursPeriod>}
     * @memberof MenuSectionAvailability
     */
    'AvailableTimes'?: Array<BusinessHoursPeriod>;
    /**
     * Availability mode
     * @type {string}
     * @memberof MenuSectionAvailability
     */
    'AvailabilityMode'?: MenuSectionAvailabilityAvailabilityModeEnum | null;
}

export const MenuSectionAvailabilityAvailabilityModeEnum = {
    DisplayAlways: 'DisplayAlways',
    DisplayBasedOnTimes: 'DisplayBasedOnTimes',
    DisplayAlwaysStartCollapsed: 'DisplayAlwaysStartCollapsed',
    DisplayAlwaysStartCollapsedBasedOnTimes: 'DisplayAlwaysStartCollapsedBasedOnTimes'
} as const;

export type MenuSectionAvailabilityAvailabilityModeEnum = typeof MenuSectionAvailabilityAvailabilityModeEnum[keyof typeof MenuSectionAvailabilityAvailabilityModeEnum];


/* tslint:disable */
/* eslint-disable */
/**
 * Flipdish Open API v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { BusinessHoursPeriod } from './business-hours-period';
// May contain unused imports in some cases
// @ts-ignore
import type { CreateMetadata } from './create-metadata';
// May contain unused imports in some cases
// @ts-ignore
import type { MenuItemOptionSet } from './menu-item-option-set';

/**
 * Menu item
 * @export
 * @interface MenuSectionItem
 */
export interface MenuSectionItem {
    /**
     * Actual price - the minimum price possible when all required option set items prices are included.
     * @type {number}
     * @memberof MenuSectionItem
     */
    'ActualPrice'?: number;
    /**
     * Menu item option sets
     * @type {Array<MenuItemOptionSet>}
     * @memberof MenuSectionItem
     */
    'MenuItemOptionSets'?: Array<MenuItemOptionSet>;
    /**
     * Daily special hours
     * @type {Array<BusinessHoursPeriod>}
     * @memberof MenuSectionItem
     */
    'DailySpecialHours'?: Array<BusinessHoursPeriod>;
    /**
     * Permanent reference to the item.
     * @type {string}
     * @memberof MenuSectionItem
     */
    'PublicId'?: string;
    /**
     * Tax rate name
     * @type {string}
     * @memberof MenuSectionItem
     */
    'TaxRateName'?: string;
    /**
     * TaxRate
     * @type {number}
     * @memberof MenuSectionItem
     */
    'TaxRateId'?: number | null;
    /**
     * TaxValue - the tax associated with this item, based on TaxRate / TaxType and Currency (currency determines decimal point precision)
     * @type {number}
     * @memberof MenuSectionItem
     */
    'TaxValue'?: number;
    /**
     * If true, the item is excluded from voucher discount calculations
     * @type {boolean}
     * @memberof MenuSectionItem
     */
    'ExcludeFromVoucherDiscounting'?: boolean;
    /**
     * List of metadata
     * @type {Array<CreateMetadata>}
     * @memberof MenuSectionItem
     */
    'Metadata'?: Array<CreateMetadata>;
    /**
     * Catalog item Id when the Item is associated to a Product
     * @type {string}
     * @memberof MenuSectionItem
     */
    'CatalogItemId'?: string;
    /**
     * Menu item name (like \"Korma\")
     * @type {string}
     * @memberof MenuSectionItem
     */
    'Name'?: string;
    /**
     * Description (like \"A lovely dish from the east\")
     * @type {string}
     * @memberof MenuSectionItem
     */
    'Description'?: string;
    /**
     * Spiciness rating
     * @type {string}
     * @memberof MenuSectionItem
     */
    'SpicinessRating'?: MenuSectionItemSpicinessRatingEnum;
    /**
     * Price - this is only used when there is no master option set and should be set to 0 if a master option set exists.
     * @type {number}
     * @memberof MenuSectionItem
     */
    'Price'?: number;
    /**
     * An optional fee that can be added to the price of the item.
     * @type {number}
     * @memberof MenuSectionItem
     */
    'DepositReturnFee'?: number | null;
    /**
     * Display order
     * @type {number}
     * @memberof MenuSectionItem
     */
    'DisplayOrder'?: number;
    /**
     * To be set true if the item or an option of the item contains an alcoholic drink.
     * @type {boolean}
     * @memberof MenuSectionItem
     */
    'Alcohol'?: boolean;
    /**
     * True if we accept orders for this item still
     * @type {boolean}
     * @memberof MenuSectionItem
     */
    'IsAvailable'?: boolean;
    /**
     * Small | Medium | Large  Affects the layout of the menu.
     * @type {string}
     * @memberof MenuSectionItem
     */
    'CellLayoutType'?: MenuSectionItemCellLayoutTypeEnum;
    /**
     * If true, then vouchers won\'t be applied for this item
     * @type {boolean}
     * @memberof MenuSectionItem
     */
    'DisableVouchers'?: boolean;
    /**
     * Image url
     * @type {string}
     * @memberof MenuSectionItem
     */
    'ImageName'?: string;
    /**
     * Image url
     * @type {string}
     * @memberof MenuSectionItem
     */
    'ImageUrl'?: string;
    /**
     * Menu Item Id
     * @type {number}
     * @memberof MenuSectionItem
     */
    'MenuItemId'?: number;
}

export const MenuSectionItemSpicinessRatingEnum = {
    NotRated: 'NotRated',
    Mild: 'Mild',
    Medium: 'Medium',
    Hot: 'Hot'
} as const;

export type MenuSectionItemSpicinessRatingEnum = typeof MenuSectionItemSpicinessRatingEnum[keyof typeof MenuSectionItemSpicinessRatingEnum];
export const MenuSectionItemCellLayoutTypeEnum = {
    Small: 'Small',
    Medium: 'Medium',
    Large: 'Large',
    HiddenImage: 'HiddenImage'
} as const;

export type MenuSectionItemCellLayoutTypeEnum = typeof MenuSectionItemCellLayoutTypeEnum[keyof typeof MenuSectionItemCellLayoutTypeEnum];


/* tslint:disable */
/* eslint-disable */
/**
 * Flipdish Open API v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { MenuSectionAvailability } from './menu-section-availability';
// May contain unused imports in some cases
// @ts-ignore
import type { MenuSectionItem } from './menu-section-item';

/**
 * Menu section
 * @export
 * @interface MenuSection
 */
export interface MenuSection {
    /**
     * Menu section identifier
     * @type {number}
     * @memberof MenuSection
     */
    'MenuSectionId'?: number;
    /**
     * Menu items
     * @type {Array<MenuSectionItem>}
     * @memberof MenuSection
     */
    'MenuItems'?: Array<MenuSectionItem>;
    /**
     * 
     * @type {MenuSectionAvailability}
     * @memberof MenuSection
     */
    'MenuSectionAvailability'?: MenuSectionAvailability;
    /**
     * Permanent reference to the item.
     * @type {string}
     * @memberof MenuSection
     */
    'PublicId'?: string;
    /**
     * Image Name
     * @type {string}
     * @memberof MenuSection
     */
    'ImageName'?: string;
    /**
     * Name
     * @type {string}
     * @memberof MenuSection
     */
    'Name'?: string;
    /**
     * Description
     * @type {string}
     * @memberof MenuSection
     */
    'Description'?: string;
    /**
     * Display order
     * @type {number}
     * @memberof MenuSection
     */
    'DisplayOrder'?: number;
    /**
     * Is available
     * @type {boolean}
     * @memberof MenuSection
     */
    'IsAvailable'?: boolean;
    /**
     * Is hidden from customer. Perhaps when the item is out of stock.
     * @type {boolean}
     * @memberof MenuSection
     */
    'IsHiddenFromCustomers'?: boolean;
    /**
     * Image url
     * @type {string}
     * @memberof MenuSection
     */
    'ImageUrl'?: string;
}

/* tslint:disable */
/* eslint-disable */
/**
 * Flipdish Open API v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * Tax Rates Associated with a Menu
 * @export
 * @interface MenuTaxRate
 */
export interface MenuTaxRate {
    /**
     * Id of Tax Rate
     * @type {number}
     * @memberof MenuTaxRate
     */
    'TaxRateId'?: number;
    /**
     * Name of Tax Rate
     * @type {string}
     * @memberof MenuTaxRate
     */
    'Name'?: string;
    /**
     * In Percentage
     * @type {number}
     * @memberof MenuTaxRate
     */
    'Rate'?: number;
}

/* tslint:disable */
/* eslint-disable */
/**
 * Flipdish Open API v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * A class that represents a single opening period in a day.  This starts \'StartTime\' after midnight and runs for a \'Period\'  after that, on the given DayOfWeek.
 * @export
 * @interface Range
 */
export interface Range {
    /**
     * Start time
     * @type {string}
     * @memberof Range
     */
    'StartTime'?: string;
    /**
     * Period
     * @type {string}
     * @memberof Range
     */
    'Period'?: string;
    /**
     * Day of week
     * @type {string}
     * @memberof Range
     */
    'DayOfWeek'?: RangeDayOfWeekEnum;
}

export const RangeDayOfWeekEnum = {
    Sunday: 'Sunday',
    Monday: 'Monday',
    Tuesday: 'Tuesday',
    Wednesday: 'Wednesday',
    Thursday: 'Thursday',
    Friday: 'Friday',
    Saturday: 'Saturday'
} as const;

export type RangeDayOfWeekEnum = typeof RangeDayOfWeekEnum[keyof typeof RangeDayOfWeekEnum];


