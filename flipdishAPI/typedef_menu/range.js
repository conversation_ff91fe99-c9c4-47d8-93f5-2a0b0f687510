/* tslint:disable */
/* eslint-disable */
/**
 * Flipdish Open API v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * A class that represents a single opening period in a day.  This starts \'StartTime\' after midnight and runs for a \'Period\'  after that, on the given DayOfWeek.
 * @export
 * @interface Range
 */
export interface Range {
    /**
     * Start time
     * @type {string}
     * @memberof Range
     */
    'StartTime'?: string;
    /**
     * Period
     * @type {string}
     * @memberof Range
     */
    'Period'?: string;
    /**
     * Day of week
     * @type {string}
     * @memberof Range
     */
    'DayOfWeek'?: RangeDayOfWeekEnum;
}

export const RangeDayOfWeekEnum = {
    Sunday: 'Sunday',
    Monday: 'Monday',
    Tuesday: 'Tuesday',
    Wednesday: 'Wednesday',
    Thursday: 'Thursday',
    Friday: 'Friday',
    Saturday: 'Saturday'
} as const;

export type RangeDayOfWeekEnum = typeof RangeDayOfWeekEnum[keyof typeof RangeDayOfWeekEnum];


