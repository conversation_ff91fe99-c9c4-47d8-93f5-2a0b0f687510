/* tslint:disable */
/* eslint-disable */
/**
 * Flipdish Open API v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { MenuItemOptionSetItem } from './menu-item-option-set-item';

/**
 * Menu item option set
 * @export
 * @interface MenuItemOptionSet
 */
export interface MenuItemOptionSet {
    /**
     * Menu item option set identifier
     * @type {number}
     * @memberof MenuItemOptionSet
     */
    'MenuItemOptionSetId'?: number;
    /**
     * Image Name
     * @type {string}
     * @memberof MenuItemOptionSet
     */
    'ImageName'?: string;
    /**
     * Image url
     * @type {string}
     * @memberof MenuItemOptionSet
     */
    'ImageUrl'?: string;
    /**
     * Option set items
     * @type {Array<MenuItemOptionSetItem>}
     * @memberof MenuItemOptionSet
     */
    'MenuItemOptionSetItems'?: Array<MenuItemOptionSetItem>;
    /**
     * Permanent reference to the item.
     * @type {string}
     * @memberof MenuItemOptionSet
     */
    'PublicId'?: string;
    /**
     * Catalog item Id when the OptionSet is associated to a Product
     * @type {string}
     * @memberof MenuItemOptionSet
     */
    'CatalogItemId'?: string;
    /**
     * Menu item option set name
     * @type {string}
     * @memberof MenuItemOptionSet
     */
    'Name'?: string;
    /**
     * Is master option set. This can affect the layout of the options in the menu displayed to the customer. Usually it is true if the option could be considerd a standalone item as opposed to an addition (\"with ketchup\") or modifier (\"large\").
     * @type {boolean}
     * @memberof MenuItemOptionSet
     */
    'IsMasterOptionSet'?: boolean;
    /**
     * Display order. Displayed in ascending order.
     * @type {number}
     * @memberof MenuItemOptionSet
     */
    'DisplayOrder'?: number;
    /**
     * Minimum items must be selected
     * @type {number}
     * @memberof MenuItemOptionSet
     */
    'MinSelectCount'?: number;
    /**
     * Maximum number of items can be selected
     * @type {number}
     * @memberof MenuItemOptionSet
     */
    'MaxSelectCount'?: number;
    /**
     * Small | Medium | Large  Affects the layout of the menu.
     * @type {string}
     * @memberof MenuItemOptionSet
     */
    'CellLayoutType'?: MenuItemOptionSetCellLayoutTypeEnum;
}

export const MenuItemOptionSetCellLayoutTypeEnum = {
    Small: 'Small',
    Medium: 'Medium',
    Large: 'Large',
    HiddenImage: 'HiddenImage'
} as const;

export type MenuItemOptionSetCellLayoutTypeEnum = typeof MenuItemOptionSetCellLayoutTypeEnum[keyof typeof MenuItemOptionSetCellLayoutTypeEnum];


