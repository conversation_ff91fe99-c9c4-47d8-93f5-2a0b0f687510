/* tslint:disable */
/* eslint-disable */
/**
 * Flipdish Open API v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { MenuSection } from './menu-section';
// May contain unused imports in some cases
// @ts-ignore
import type { MenuTaxRate } from './menu-tax-rate';

/**
 * Menu
 * @export
 * @interface Menu
 */
export interface Menu {
    /**
     * Menu identifier
     * @type {number}
     * @memberof Menu
     */
    'MenuId'?: number;
    /**
     * Last modified time
     * @type {string}
     * @memberof Menu
     */
    'ModifiedTime'?: string;
    /**
     * Menu version
     * @type {number}
     * @memberof Menu
     */
    'VersionNumber'?: number;
    /**
     * Image Name
     * @type {string}
     * @memberof Menu
     */
    'ImageName'?: string;
    /**
     * Image url
     * @type {string}
     * @memberof Menu
     */
    'ImageUrl'?: string;
    /**
     * Name of Menu, only shown in portal
     * @type {string}
     * @memberof Menu
     */
    'Name'?: string;
    /**
     * Locked: is menu locked against modifcation
     * @type {boolean}
     * @memberof Menu
     */
    'Locked'?: boolean;
    /**
     * Menu sections (startes, main etc)
     * @type {Array<MenuSection>}
     * @memberof Menu
     */
    'MenuSections'?: Array<MenuSection>;
    /**
     * Menu tax rates
     * @type {Array<MenuTaxRate>}
     * @memberof Menu
     */
    'TaxRates'?: Array<MenuTaxRate>;
    /**
     * Identifier of App the menu is attached to
     * @type {string}
     * @memberof Menu
     */
    'AppId'?: string;
    /**
     * Display menu section link on UI
     * @type {boolean}
     * @memberof Menu
     */
    'DisplaySectionLinks'?: boolean;
    /**
     * Menu section behaviour
     * @type {string}
     * @memberof Menu
     */
    'MenuSectionBehaviour'?: MenuMenuSectionBehaviourEnum;
    /**
     * Tax type
     * @type {string}
     * @memberof Menu
     */
    'TaxType'?: MenuTaxTypeEnum;
    /**
     * Flag to indicate if the menu is integrated (contains metadata)
     * @type {boolean}
     * @memberof Menu
     */
    'IsIntegrated'?: boolean;
}

export const MenuMenuSectionBehaviourEnum = {
    ExpandSingle: 'ExpandSingle',
    ExpandMultiple: 'ExpandMultiple'
} as const;

export type MenuMenuSectionBehaviourEnum = typeof MenuMenuSectionBehaviourEnum[keyof typeof MenuMenuSectionBehaviourEnum];
export const MenuTaxTypeEnum = {
    IncludedInBasePrice: 'IncludedInBasePrice',
    ExcludedFromBasePrice: 'ExcludedFromBasePrice'
} as const;

export type MenuTaxTypeEnum = typeof MenuTaxTypeEnum[keyof typeof MenuTaxTypeEnum];


