/* tslint:disable */
/* eslint-disable */
/**
 * Flipdish Open API v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * Tax Rates Associated with a Menu
 * @export
 * @interface MenuTaxRate
 */
export interface MenuTaxRate {
    /**
     * Id of Tax Rate
     * @type {number}
     * @memberof MenuTaxRate
     */
    'TaxRateId'?: number;
    /**
     * Name of Tax Rate
     * @type {string}
     * @memberof MenuTaxRate
     */
    'Name'?: string;
    /**
     * In Percentage
     * @type {number}
     * @memberof MenuTaxRate
     */
    'Rate'?: number;
}

