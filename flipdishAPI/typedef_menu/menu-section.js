/* tslint:disable */
/* eslint-disable */
/**
 * Flipdish Open API v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { MenuSectionAvailability } from './menu-section-availability';
// May contain unused imports in some cases
// @ts-ignore
import type { MenuSectionItem } from './menu-section-item';

/**
 * Menu section
 * @export
 * @interface MenuSection
 */
export interface MenuSection {
    /**
     * Menu section identifier
     * @type {number}
     * @memberof MenuSection
     */
    'MenuSectionId'?: number;
    /**
     * Menu items
     * @type {Array<MenuSectionItem>}
     * @memberof MenuSection
     */
    'MenuItems'?: Array<MenuSectionItem>;
    /**
     * 
     * @type {MenuSectionAvailability}
     * @memberof MenuSection
     */
    'MenuSectionAvailability'?: MenuSectionAvailability;
    /**
     * Permanent reference to the item.
     * @type {string}
     * @memberof MenuSection
     */
    'PublicId'?: string;
    /**
     * Image Name
     * @type {string}
     * @memberof MenuSection
     */
    'ImageName'?: string;
    /**
     * Name
     * @type {string}
     * @memberof MenuSection
     */
    'Name'?: string;
    /**
     * Description
     * @type {string}
     * @memberof MenuSection
     */
    'Description'?: string;
    /**
     * Display order
     * @type {number}
     * @memberof MenuSection
     */
    'DisplayOrder'?: number;
    /**
     * Is available
     * @type {boolean}
     * @memberof MenuSection
     */
    'IsAvailable'?: boolean;
    /**
     * Is hidden from customer. Perhaps when the item is out of stock.
     * @type {boolean}
     * @memberof MenuSection
     */
    'IsHiddenFromCustomers'?: boolean;
    /**
     * Image url
     * @type {string}
     * @memberof MenuSection
     */
    'ImageUrl'?: string;
}

