// test/data/restaurant_pizza_place.json
{
  "_id": "restaurant_id_pizza_place",
  "name": "The Pizza Place",
  "options": [
    {
      "_id": "opt_size_small",
      "title": "Small",
      "description": "8 inch",
      "price": 0.0
    },
    {
      "_id": "opt_size_medium",
      "title": "Medium",
      "description": "12 inch",
      "price": 2.50
    },
    {
      "_id": "opt_size_large",
      "title": "Large",
      "description": "16 inch",
      "price": 5.00
    },
    {
      "_id": "opt_crust_thin",
      "title": "Thin Crust",
      "price": 0.0
    },
    {
      "_id": "opt_crust_thick",
      "title": "Thick Crust",
      "price": 1.00
    },
    {
      "_id": "opt_topping_pepperoni",
      "title": "Pepperoni",
      "price": 1.50
    },
    {
      "_id": "opt_topping_mushrooms",
      "title": "Mushrooms",
      "price": 1.00
    },
    {
      "_id": "opt_topping_olives",
      "title": "Olives",
      "price": 0.75
    },
    {
      "_id": "opt_extra_cheese_yes",
      "title": "Yes",
      "price": 2.00
    }
  ],
  "addons": [
    {
      "_id": "addon_pizza_toppings",
      "title": "Pizza Toppings",
      "description": "Choose your favorite toppings",
      "options": ["opt_topping_pepperoni", "opt_topping_mushrooms", "opt_topping_olives"],
      "quantity_minimum": 0,
      "quantity_maximum": 3
    },
    {
      "_id": "addon_extra_cheese",
      "title": "Extra Cheese",
      "description": "Add an extra layer of cheese",
      "options": ["opt_extra_cheese_yes"],
      "quantity_minimum": 0,
      "quantity_maximum": 1
    },
    {
      "_id": "addon_crust_choice",
      "title": "Crust Choice",
      "description": "Select your preferred crust",
      "options": ["opt_crust_thin", "opt_crust_thick"],
      "quantity_minimum": 1,
      "quantity_maximum": 1
    }
  ],
  "categories": [
    {
      "_id": "cat_pizzas",
      "title": "Pizzas",
      "foods": [
        {
          "_id": "food_margherita",
          "title": "Margherita Pizza",
          "description": "Classic cheese and tomato pizza.",
          "image": "http://example.com/images/margherita.jpg",
          "is_active": true,
          "variations": [
            {
              "_id": "var_margherita_s",
              "title": "Small",
              "price": 8.99,
              "addons": ["addon_pizza_toppings", "addon_extra_cheese", "addon_crust_choice"]
            },
            {
              "_id": "var_margherita_m",
              "title": "Medium",
              "price": 12.99,
              "discounted": 11.99,
              "addons": ["addon_pizza_toppings", "addon_extra_cheese", "addon_crust_choice"]
            },
            {
              "_id": "var_margherita_l",
              "title": "Large",
              "price": 15.99,
              "addons": ["addon_pizza_toppings", "addon_extra_cheese", "addon_crust_choice"]
            }
          ]
        },
        {
          "_id": "food_pepperoni_pizza",
          "title": "Pepperoni Feast Pizza",
          "description": "Loaded with pepperoni and cheese.",
          "image": "http://example.com/images/pepperoni.jpg",
          "is_active": true,
          "variations": [
            {
              "_id": "var_pepperoni_m",
              "title": "Medium",
              "price": 14.99,
              "addons": ["addon_extra_cheese", "addon_crust_choice"]
            },
            {
              "_id": "var_pepperoni_l",
              "title": "Large",
              "price": 18.99,
              "addons": ["addon_extra_cheese", "addon_crust_choice"]
            }
          ]
        }
      ]
    },
    {
      "_id": "cat_sides",
      "title": "Sides",
      "foods": [
        {
          "_id": "food_garlic_bread",
          "title": "Garlic Bread",
          "description": "Warm and toasty garlic bread.",
          "is_active": true,
          "variations": [
            {
              "_id": "var_garlic_bread_reg",
              "title": "Regular",
              "price": 4.50,
              "addons": ["addon_extra_cheese"]
            }
          ]
        }
      ]
    },
    {
      "_id": "cat_drinks_pizza_place",
      "title": "Drinks",
      "foods": [
        {
          "_id": "food_soda_pizza",
          "title": "Soda",
          "description": "Refreshing carbonated beverage.",
          "is_active": true,
          "variations": [
            {
              "_id": "var_soda_can_pizza",
              "title": "Can",
              "price": 1.50,
              "addons": []
            },
            {
              "_id": "var_soda_bottle_pizza",
              "title": "Bottle (2L)",
              "price": 3.00,
              "addons": []
            }
          ]
        }
      ]
    }
  ]
}