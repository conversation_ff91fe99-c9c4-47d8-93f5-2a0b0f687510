// test/data/restaurant_burger_bonanza.json
{
  "_id": "restaurant_id_burger_bonanza",
  "name": "Burger Bonanza",
  "options": [
    {
      "_id": "opt_bun_sesame",
      "title": "Sesame Bun",
      "price": 0.0
    },
    {
      "_id": "opt_bun_brioche",
      "title": "Brioche Bun",
      "price": 1.00
    },
    {
      "_id": "opt_patty_single",
      "title": "Single Patty",
      "price": 0.0
    },
    {
      "_id": "opt_patty_double",
      "title": "Double Patty",
      "price": 2.50
    },
    {
      "_id": "opt_sauce_ketchup",
      "title": "Ketchup",
      "price": 0.0
    },
    {
      "_id": "opt_sauce_mayo",
      "title": "Mayonnaise",
      "price": 0.0
    },
    {
      "_id": "opt_sauce_bbq",
      "title": "BBQ Sauce",
      "price": 0.25
    },
    {
      "_id": "opt_side_fries",
      "title": "French Fries",
      "price": 2.00
    },
    {
      "_id": "opt_side_rings",
      "title": "Onion Rings",
      "price": 2.50
    },
    {
      "_id": "opt_cheese_slice_yes",
      "title": "Yes",
      "price": 0.75
    }
  ],
  "addons": [
    {
      "_id": "addon_bun_choice",
      "title": "Bun Choice",
      "description": "Select your bun",
      "options": ["opt_bun_sesame", "opt_bun_brioche"],
      "quantity_minimum": 1,
      "quantity_maximum": 1
    },
    {
      "_id": "addon_burger_sauces",
      "title": "Sauces",
      "description": "Add your favorite sauces",
      "options": ["opt_sauce_ketchup", "opt_sauce_mayo", "opt_sauce_bbq"],
      "quantity_minimum": 0,
      "quantity_maximum": 2
    },
    {
      "_id": "addon_side_choice",
      "title": "Choose a Side",
      "description": "Pick a delicious side",
      "options": ["opt_side_fries", "opt_side_rings"],
      "quantity_minimum": 0,
      "quantity_maximum": 1
    },
    {
      "_id": "addon_add_cheese_slice",
      "title": "Add Cheese Slice",
      "options": ["opt_cheese_slice_yes"],
      "quantity_minimum": 0,
      "quantity_maximum": 1
    }
  ],
  "categories": [
    {
      "_id": "cat_burgers",
      "title": "Signature Burgers",
      "foods": [
        {
          "_id": "food_classic_burger",
          "title": "Classic Burger",
          "description": "A timeless classic beef burger.",
          "image": "http://example.com/images/classic_burger.jpg",
          "is_active": true,
          "variations": [
            {
              "_id": "var_classic_single",
              "title": "Single Patty",
              "price": 7.99,
              "addons": ["addon_bun_choice", "addon_burger_sauces", "addon_side_choice", "addon_add_cheese_slice"]
            },
            {
              "_id": "var_classic_double",
              "title": "Double Patty",
              "price": 10.49,
              "addons": ["addon_bun_choice", "addon_burger_sauces", "addon_side_choice", "addon_add_cheese_slice"]
            }
          ]
        },
        {
          "_id": "food_bbq_burger",
          "title": "BBQ Bacon Burger",
          "description": "Smoky BBQ sauce and crispy bacon.",
          "image": "http://example.com/images/bbq_burger.jpg",
          "is_active": true,
          "variations": [
            {
              "_id": "var_bbq_single",
              "title": "Single Patty",
              "price": 9.50,
              "discounted": 8.99,
              "addons": ["addon_bun_choice", "addon_side_choice", "addon_add_cheese_slice"]
            },
            {
              "_id": "var_bbq_double",
              "title": "Double Patty",
              "price": 12.00,
              "addons": ["addon_bun_choice", "addon_side_choice", "addon_add_cheese_slice"]
            }
          ]
        }
      ]
    },
    {
      "_id": "cat_beverages_burger",
      "title": "Beverages",
      "foods": [
        {
          "_id": "food_milkshake",
          "title": "Milkshake",
          "description": "Creamy and delicious milkshakes.",
          "is_active": true,
          "variations": [
            {
              "_id": "var_shake_choco",
              "title": "Chocolate",
              "price": 4.00,
              "addons": []
            },
            {
              "_id": "var_shake_vanilla",
              "title": "Vanilla",
              "price": 4.00,
              "addons": []
            },
            {
              "_id": "var_shake_strawberry",
              "title": "Strawberry",
              "price": 4.25,
              "addons": []
            }
          ]
        }
      ]
    }
  ]
}