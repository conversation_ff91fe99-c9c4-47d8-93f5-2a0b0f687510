Convert flipdish like menu structure to firespoon structure using python.
:

数据映射关系
MenuSections -> category
    MenuItem  -> food
        MenuItemOptionSet -> addon
            MenuItemOptionSetItems -> option

执行步骤:
读取参数后, 
第一步, 提取所有的MenuItemOptionSetItems, 并去重: 只保留options需要的字段. 但是要保留MenuItemOptionSetItems的_id, 用于后续的addon和option的关联
第二步, 提取所有的MenuItemOptionSet, 并去重: 只保留addon需要的字段. 但是要保留MenuItemOptionSet的_id, 用于后续的addon和option的关联, 将其内部包含的MenuItemOptionSetItems, 替换为options的id
第三步, 提取所有的MenuItem, 并去重: 只保留food需要的字段. 但是要保留MenuItem的_id, 用于后续的addon和option的关联, 将其内部包含的MenuItemOptionSet, 替换为addon的id
第四步, 提取所有的MenuSection, 并去重: 只保留category需要的字段. 但是要保留MenuSection的_id, 用于后续的addon和option的关联, 将其内部包含的MenuItem,替换为food的id
第五步:
按照输出格式要求, 从各个步骤的输出中, 构造出restaurant.json. 其中variation的字段,分几种情况:
1. 如果food下的addons为空, 以food的描述构造唯一的variation,variation的addons为空
2. 如果food下的addons不为空, 且有"size"为名字的addon, 按照size的options构造variation, 其中每个variantion下面的addon都是原来的food的addons (除了size之外)
3. 如果food下的addons不为空, 且没有"size"为名字的addon, 以food的描述构造唯一的variation, 将food原来的addons, 作为variation的addons

需要将1-4步的结果写入log文件.其中
文件addon.csv, 每行写入一个addon的信息(不含option的id), 以及option的内容, 全部以csv格式写入. 也就是说如果一个addon有四个option, 那就分成四行, 每行都是 addon + 一个option.
要将所有option全部写入.

文件food.csv, 按顺序写:
catagary名称
换行写入本category下的food:
每行写入一个food的信息(不含variation的id), 以及variation的内容(其中addon只写id), 全部以csv格式写入. 也就是说如果一个food有四个variation, 那就分成四行, 每行都是 food + 一个variation.
然后再写下一个category.



导入.json文件到graphql服务器
第一步: 导入options, 
第二步, 查询restaurant, 从而获取全部options的id和name, description, price. 根据这些数据组织addon的输入: addon的option里面的option的id, 要替换成新的数据库里面的ID, 然后通过graphQL接口导入addons
第三步, 查询restaurant, 从而获取全部addons的id和name, description, options. 根据这些数据组织food的输入: food的variation里面的addon的id, 要替换成新的数据库里面的ID, 然后通过graphQL接口导入foods
